<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}纸落云烟帮会管理系统{% endblock %}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .nav-tabs {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-tab {
            display: inline-block;
            padding: 12px 25px;
            margin: 0 5px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            color: #555;
        }

        .nav-tab:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .team-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-5px);
        }

        .team-header {
            padding: 20px;
            color: white;
            font-weight: bold;
            font-size: 1.3em;
            text-align: center;
        }

        .team-1 .team-header { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .team-2 .team-header { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .team-3 .team-header { background: linear-gradient(135deg, #45b7d1, #96c93d); }
        .team-4 .team-header { background: linear-gradient(135deg, #96ceb4, #ffecd2); color: #333; }

        .team-content {
            padding: 20px;
        }

        .member-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .member-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .member-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .member-details {
            font-size: 0.9em;
            color: #7f8c8d;
        }

        .profession-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-right: 5px;
        }

        .profession-素问 { background: #ffebee; color: #c62828; }
        .profession-铁衣 { background: #fff3e0; color: #ef6c00; }
        .profession-潮光 { background: #e1f5fe; color: #0277bd; }
        .profession-九灵 { background: #f3e5f5; color: #7b1fa2; }
        .profession-龙吟 { background: #e8f5e8; color: #2e7d32; }
        .profession-血河 { background: #ffebee; color: #d32f2f; }
        .profession-碎梦 { background: #e0f2f1; color: #00695c; }
        .profession-玄机 { background: #fff9c4; color: #f57f17; }
        .profession-神相 { background: #e8eaf6; color: #3f51b5; }
        .profession-沧澜 { background: #f1f8e9; color: #558b2f; }

        .edit-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .edit-btn:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }

        .search-box {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .search-box:focus {
            outline: none;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .hidden {
            display: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h1>🏮 纸落云烟帮会管理系统</h1>
                    <div class="subtitle">成员详情 | 团队配置 | 数据统计</div>
                </div>
                {% if get_current_user() %}
                <div style="text-align: right;">
                    <div style="margin-bottom: 10px;">
                        <span style="color: #667eea; font-weight: bold;">{{ get_current_user().name }}</span>
                        <span style="margin-left: 10px; padding: 4px 8px; background: {% if is_super_admin() %}#dc3545{% elif is_guild_leader() %}#ff6b6b{% elif is_guest() %}#6c757d{% else %}#4ecdc4{% endif %}; color: white; border-radius: 12px; font-size: 12px;">
                            {% if is_super_admin() %}超级管理员{% elif is_guild_leader() %}大当家{% elif is_guest() %}游客{% else %}普通用户{% endif %}
                        </span>
                    </div>
                    <a href="{{ url_for('logout') }}" style="color: #dc3545; text-decoration: none; font-size: 14px;">
                        🚪 退出登录
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab" onclick="showPage('index')">📊 总览</button>
            {% if can_edit() %}
            <button class="nav-tab" onclick="showPage('drag')">🎯 拖拽排表</button>
            {% endif %}
            <button class="nav-tab" onclick="showPage('members')">👥 成员详情</button>
            <button class="nav-tab" onclick="showPage('organization')">🏗️ 组织架构</button>
            <button class="nav-tab" onclick="showPage('battle')">⚔️ 战斗分析</button>
            {% if get_current_user() and get_current_user().role == 'user' %}
            <button class="nav-tab" onclick="showPage('guild_application')">🏛️ 帮会申请</button>
            {% endif %}
            {% if is_super_admin() or is_guild_leader() %}
            <button class="nav-tab" onclick="showPage('admin_applications')">📋 申请管理</button>
            {% endif %}
        </div>

        {% block content %}{% endblock %}
    </div>

    {% block scripts %}{% endblock %}

    <script>
        function showPage(page) {
            // 移除所有active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 添加active类到当前标签
            event.target.classList.add('active');

            // 根据页面类型跳转
            if (page === 'index') {
                window.location.href = '/';
            } else if (page === 'drag') {
                window.location.href = '/drag_board';
            } else if (page === 'members') {
                window.location.href = '/members';
            } else if (page === 'organization') {
                window.location.href = '/organization_chart';
            } else if (page === 'battle') {
                window.location.href = '/battle_analysis';
            } else if (page === 'guild_application') {
                window.location.href = '/guild_application';
            } else if (page === 'admin_applications') {
                window.location.href = '/admin/applications';
            }
        }

        // 根据当前页面设置active标签
        function setActiveTab() {
            const path = window.location.pathname;
            const tabs = document.querySelectorAll('.nav-tab');

            tabs.forEach(tab => tab.classList.remove('active'));

            // 根据按钮的onclick属性来匹配，而不是索引
            tabs.forEach(tab => {
                const onclick = tab.getAttribute('onclick');
                if (!onclick) return;

                if ((path === '/' || path === '/index') && onclick.includes("'index'")) {
                    tab.classList.add('active');  // 总览
                } else if (path === '/drag_board' && onclick.includes("'drag'")) {
                    tab.classList.add('active');  // 拖拽排表
                } else if (path === '/members' && onclick.includes("'members'")) {
                    tab.classList.add('active');  // 成员详情
                } else if (path === '/organization_chart' && onclick.includes("'organization'")) {
                    tab.classList.add('active');  // 组织架构
                } else if (path === '/battle_analysis' && onclick.includes("'battle'")) {
                    tab.classList.add('active');  // 战斗分析
                } else if (path === '/guild_application' && onclick.includes("'guild_application'")) {
                    tab.classList.add('active');  // 帮会申请
                } else if (path === '/admin/applications' && onclick.includes("'admin_applications'")) {
                    tab.classList.add('active');  // 申请管理
                }
            });
        }

        // 页面加载时设置active标签
        document.addEventListener('DOMContentLoaded', setActiveTab);
    </script>
</body>
</html>
